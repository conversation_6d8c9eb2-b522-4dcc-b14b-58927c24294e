# Quick deployment script to fix the Lambda function for product ID fetch
# This deploys the updated Lambda function that handles ID-only queries

Write-Host "🚀 Deploying Lambda function fix for product ID fetch..." -ForegroundColor Yellow

# Change to Lambda directory
Set-Location "lambda/dev/ListProducts"

Write-Host "📦 Building Lambda function..." -ForegroundColor Cyan
$env:GOOS = "linux"
$env:GOARCH = "amd64"
go build -o bootstrap listProducts.go

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "📦 Creating deployment package..." -ForegroundColor Cyan
Compress-Archive -Path bootstrap -DestinationPath dev-list-products.zip -Force

Write-Host "☁️  Uploading to AWS Lambda..." -ForegroundColor Cyan
aws lambda update-function-code `
    --function-name dev-list-products `
    --zip-file fileb://dev-list-products.zip `
    --profile thealpinestudio `
    --region us-west-2

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ AWS Lambda update failed!" -ForegroundColor Red
    exit 1
}

Write-Host "🧹 Cleaning up..." -ForegroundColor Cyan
Remove-Item bootstrap -ErrorAction SilentlyContinue
Remove-Item dev-list-products.zip -ErrorAction SilentlyContinue

Write-Host "✅ Lambda function updated successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "🔍 Testing the fix:" -ForegroundColor Yellow
Write-Host "The Lambda function now supports fetching products by ID only using query parameters:" -ForegroundColor White
Write-Host "Example: https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test/dev-list-products?id=1743209557" -ForegroundColor Cyan
Write-Host ""
Write-Host "The frontend should now work correctly when fetching individual products!" -ForegroundColor Green
