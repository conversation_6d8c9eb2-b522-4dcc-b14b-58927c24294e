# Script to clean up orphaned API Gateway resources from Terraform state
# This addresses the "No integration defined for method" error

Write-Host "🧹 Cleaning up orphaned API Gateway resources from Terraform state..." -ForegroundColor Yellow

# Change to the terraform directory
Set-Location "terraform/development/dev-env"

# Initialize terraform if needed
terraform init

# List of potentially orphaned resources to remove from state
$OrphanedResources = @(
    "module.api_gateway.aws_api_gateway_resource.dev_products_resource",
    "module.api_gateway.aws_api_gateway_resource.dev_products_id_resource",
    "module.api_gateway.aws_api_gateway_method.dev_products_get_method",
    "module.api_gateway.aws_api_gateway_method.dev_products_options_method",
    "module.api_gateway.aws_api_gateway_integration.dev_products_get_integration",
    "module.api_gateway.aws_api_gateway_integration.dev_products_options_integration",
    "module.api_gateway.aws_api_gateway_method_response.dev_products_get_response",
    "module.api_gateway.aws_api_gateway_method_response.dev_products_options_response",
    "module.api_gateway.aws_api_gateway_integration_response.dev_products_get_integration_response",
    "module.api_gateway.aws_api_gateway_integration_response.dev_products_options_integration_response",
    "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_get_product_by_id"
)

# Remove orphaned resources from state
foreach ($resource in $OrphanedResources) {
    Write-Host "🗑️  Attempting to remove $resource from state..." -ForegroundColor Cyan
    try {
        terraform state rm $resource 2>$null
        Write-Host "   ✅ Removed $resource" -ForegroundColor Green
    }
    catch {
        Write-Host "   ℹ️  Resource $resource not found in state (this is OK)" -ForegroundColor Gray
    }
}

Write-Host "✅ State cleanup completed!" -ForegroundColor Green

# Now run a plan to see what needs to be done
Write-Host "📋 Running terraform plan to verify state..." -ForegroundColor Yellow
$planResult = terraform plan -detailed-exitcode
$exitCode = $LASTEXITCODE

if ($exitCode -eq 2) {
    Write-Host "✅ Terraform plan shows changes are needed - this is expected" -ForegroundColor Green
    exit 0
} elseif ($exitCode -eq 0) {
    Write-Host "✅ Terraform state is clean and ready for deployment!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "❌ Terraform plan failed with exit code $exitCode" -ForegroundColor Red
    exit $exitCode
}
