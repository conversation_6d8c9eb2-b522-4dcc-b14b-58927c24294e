variable "s3_bucket" {
  type        = string
  description = "The name of the S3 bucket to store the lambda"
  default     = "dev-thealpinestudio-lambda-functions-v1"
}

variable "account_id" {
  type        = string
  description = "The account ID in which to create/manage resources"

}

variable "aws_region" {
  type        = string
  description = "The AWS region"
  default     = "us-west-2"
}

variable "dynamo_table_products_name" {
  type        = string
  description = "The name of the DynamoDB table for products"
  default     = "products"

}

variable "image_bucket_name" {
  type        = string
  description = "The name of the S3 bucket to store the images"
  default     = "admin-thealpinestudio-images"
}

variable "lambda_bucket" {
  type        = string
  description = "The name of the S3 bucket to store the lambda"
  default     = "dev-thealpinestudio-lambda-functions-v1"
}

variable "description" {
  type    = string
  default = "List Products function"
}

variable "lambda_iam_role_name" {
  type    = string
  default = "dev_lambda_arn_role"
}

variable "frontend_url" {
  type        = string
  description = "The URL of the frontend"
}

variable "dynamodb_orders_table" {
  type        = string
  description = "The name of the DynamoDB table for orders"
}

# variable "cognito_user_pool_arn" {
#   type        = string
#   description = "The ARN of the user pool"
# }

# API Gateway variables removed to avoid circular dependencies
# Lambda permissions are now handled by the API Gateway module

variable "resend_api_key" {
  type        = string
  description = "Resend API Key"

}

variable "stripe_secret_key" {
  type        = string
  description = "Stripe Secret Key"
}

variable "stripe_endpoint_secret" {
  type        = string
  description = "Stripe Endpoint Secret"
}
# lambda_function_arn variable removed to avoid circular dependencies

variable "list_products_function_name" {
  type        = string
  description = "the name of the lambda list function"
}

variable "checkout_products_function" {
  type        = string
  description = "Lmabda Checkout Function"
  default     = "dev-checkout-products"
}

variable "webhooks_function" {
  type        = string
  description = "Lmabda Checkout Function"
  default     = "dev-webhooks"
}

variable "contact_email_function" {
  type        = string
  description = "Lmabda Checkout Function"
  default     = "dev-contact-email"
}

variable "get_product_by_id_lambda_function_name" {
  type        = string
  description = "Lambda function name for getting product by ID"
  default     = "dev-get-product-by-id"
}

variable "region" {
  type        = string
  description = "AWS region"
  default     = "us-west-2"
}
