# PowerShell script to deploy a single development Lambda function
# Usage: .\deploy-single-lambda.ps1 -FunctionName "dev-list-products"
# ================================================================

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev-list-products", "dev-checkout-products", "dev-contact-email", "dev-webhooks")]
    [string]$FunctionName
)

# Configuration
$REGION = "us-west-2"
$PROFILE = "thealpinestudio"
$BUCKET_NAME = "dev-thealpinestudio-lambda-functions-v1"

# Function configuration mapping
$FUNCTION_CONFIG = @{
    "dev-list-products" = @{
        Directory = "..\lambda\dev\ListProducts"
        SourceFile = "listProducts.go"
        ZipFile = "list-products.zip"
        S3Key = "list-products.zip"
        TestEvent = @{
            httpMethod = "GET"
            queryStringParameters = @{}
        }
    }
    "dev-checkout-products" = @{
        Directory = "..\lambda\dev\CheckoutProducts"
        SourceFile = "checkoutProducts.go"
        ZipFile = "checkout-products.zip"
        S3Key = "checkout-products.zip"
        TestEvent = @{
            httpMethod = "POST"
            body = '{"items":[{"id":"1743209634","title":"All Eyes on You Sticker","quantity":1,"size":"Sticker","price":6}],"customerInfo":{"email":"<EMAIL>"}}'
        }
    }
    "dev-contact-email" = @{
        Directory = "..\lambda\dev\ContactEmail"
        SourceFile = "contactEmail.go"
        ZipFile = "contact-email.zip"
        S3Key = "contact-email.zip"
        TestEvent = @{
            httpMethod = "POST"
            body = '{"name":"Test User","email":"<EMAIL>","message":"This is a test message"}'
        }
    }
    "dev-webhooks" = @{
        Directory = "..\lambda\dev\Webhooks"
        SourceFile = "emailWebhook.go"
        ZipFile = "webhooks.zip"
        S3Key = "webhooks.zip"
        TestEvent = @{
            httpMethod = "POST"
            body = '{"type":"checkout.session.completed","data":{"object":{"id":"cs_test_123"}}}'
        }
    }
}

# Get function configuration
$Function = $FUNCTION_CONFIG[$FunctionName]

Write-Host "=== Deploying $FunctionName ===" -ForegroundColor Cyan

# Navigate to the function directory
Push-Location $Function.Directory

# Set environment variables for Go build
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "0"

# Build the Lambda function
Write-Host "Building Lambda function..." -ForegroundColor Yellow
go build -o bootstrap $Function.SourceFile
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed." -ForegroundColor Red
    Pop-Location
    exit 1
} else {
    Write-Host "Build succeeded." -ForegroundColor Green
}

# Zip the executable
Write-Host "Creating zip file..." -ForegroundColor Yellow
Compress-Archive -Path bootstrap -DestinationPath $Function.ZipFile -Force
if ($LASTEXITCODE -ne 0) {
    Write-Host "Zip failed." -ForegroundColor Red
    Pop-Location
    exit 1
} else {
    Write-Host "Zip succeeded." -ForegroundColor Green
}

# Upload the zipped file to S3
Write-Host "Uploading to S3..." -ForegroundColor Yellow
aws s3 cp $Function.ZipFile "s3://$BUCKET_NAME/$($Function.S3Key)" --region $REGION --profile $PROFILE
if ($LASTEXITCODE -ne 0) {
    Write-Host "S3 upload failed." -ForegroundColor Red
    Pop-Location
    exit 1
} else {
    Write-Host "S3 upload succeeded." -ForegroundColor Green
}

# Update the Lambda function with the code from S3
Write-Host "Updating Lambda function..." -ForegroundColor Yellow
aws lambda update-function-code `
    --function-name $FunctionName `
    --s3-bucket $BUCKET_NAME `
    --s3-key $Function.S3Key `
    --region $REGION `
    --profile $PROFILE
if ($LASTEXITCODE -ne 0) {
    Write-Host "Lambda function update failed." -ForegroundColor Red
    Pop-Location
    exit 1
} else {
    Write-Host "Lambda function update succeeded." -ForegroundColor Green
}

# Return to the original directory
Pop-Location

# Test the function
Write-Host "=== Testing $FunctionName ===" -ForegroundColor Cyan

# Create a temporary test event file
$testEventFile = "test-event-$FunctionName.json"
$Function.TestEvent | ConvertTo-Json -Depth 10 | Set-Content $testEventFile

# Wait a moment for the Lambda function to be ready
Start-Sleep -Seconds 5

# Invoke the Lambda function
Write-Host "Invoking Lambda function..." -ForegroundColor Yellow
$responseFile = "response-$FunctionName.json"
aws lambda invoke `
    --function-name $FunctionName `
    --cli-binary-format raw-in-base64-out `
    --payload file://$testEventFile `
    --region $REGION `
    --profile $PROFILE `
    $responseFile

# Check if the invocation was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "Lambda function invocation failed." -ForegroundColor Red
    exit 1
} else {
    Write-Host "Lambda function invocation succeeded." -ForegroundColor Green

    # Display the response
    Write-Host "Response:" -ForegroundColor Yellow
    Get-Content $responseFile | Write-Host

    # Check if the response contains an error
    $response = Get-Content $responseFile | ConvertFrom-Json
    if ($response.FunctionError) {
        Write-Host "Lambda function returned an error: $($response.FunctionError)" -ForegroundColor Red
        exit 1
    }
}

# Clean up temporary files
Remove-Item $testEventFile -ErrorAction SilentlyContinue
Remove-Item $responseFile -ErrorAction SilentlyContinue

Write-Host "`n=== Deployment and Test Complete ===" -ForegroundColor Green
