# GET PRODUCT BY ID LAMBDA FUNCTION
#----------------------------------------------------------------

data "archive_file" "lambda_code_get_product_by_id" {
  type        = "zip"
  source_dir  = "../../../lambda/dev/GetProductById/"
  output_path = "./dev-get-product-by-id.zip"
}

resource "aws_s3_object" "lambda_code_get_product_by_id" {
  bucket       = var.s3_bucket
  key          = "dev-get-product-by-id.zip"
  source       = data.archive_file.lambda_code_get_product_by_id.output_path
  etag         = filemd5(data.archive_file.lambda_code_get_product_by_id.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "get_product_by_id_function" {
  function_name = var.get_product_by_id_lambda_function_name
  s3_bucket     = aws_s3_object.lambda_code_get_product_by_id.bucket
  s3_key        = aws_s3_object.lambda_code_get_product_by_id.key
  role          = aws_iam_role.lambda_execution_role_get_product_by_id.arn
  handler       = "dev-get-product-by-id"
  runtime       = "provided.al2023"
  source_code_hash = data.archive_file.lambda_code_get_product_by_id.output_base64sha256

  environment {
    variables = {
      "TABLE_NAME"        = var.dynamo_table_products_name
      "IMAGE_BUCKET_NAME" = var.image_bucket_name
    }
  }
}

# IAM Role for Lambda execution
resource "aws_iam_role" "lambda_execution_role_get_product_by_id" {
  name = "lambda_execution_role_get_product_by_id"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# IAM Policy for Lambda to access DynamoDB and CloudWatch Logs
resource "aws_iam_policy" "lambda_policy_get_product_by_id" {
  name        = "lambda_policy_get_product_by_id"
  description = "IAM policy for Lambda to access DynamoDB and CloudWatch Logs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:Scan"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_products_name}",
          "arn:aws:dynamodb:${var.region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_products_name}/index/*"
        ]
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "lambda_policy_attachment_get_product_by_id" {
  role       = aws_iam_role.lambda_execution_role_get_product_by_id.name
  policy_arn = aws_iam_policy.lambda_policy_get_product_by_id.arn
}

# CloudWatch Log Group for Lambda function
resource "aws_cloudwatch_log_group" "lambda_log_group_get_product_by_id" {
  name              = "/aws/lambda/${var.get_product_by_id_lambda_function_name}"
  retention_in_days = 14
}
