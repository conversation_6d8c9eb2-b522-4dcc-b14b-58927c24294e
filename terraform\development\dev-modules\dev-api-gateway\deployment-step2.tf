# STEP 2: Add this back after the first deployment cleans up the state

resource "aws_api_gateway_stage" "dev_stage" {
  deployment_id = aws_api_gateway_deployment.rest_api_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  stage_name    = "test"
  variables = {
    deployed_at = timestamp()
  }
  depends_on = [aws_api_gateway_account.rest_api_account]
}

resource "aws_api_gateway_deployment" "rest_api_deployment" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id

  # Force recreation with clean state
  triggers = {
    redeployment = "clean-deployment-v2"
  }
  
  depends_on = [
    aws_api_gateway_integration.checkout_products_post_integration,
    aws_api_gateway_integration.contact_form_post_integration,
    aws_api_gateway_integration.rest_api_post_method_integration,
    aws_api_gateway_integration.rest_api_get_method_integration,
    aws_api_gateway_integration.webhooks_products_post_integration,
    aws_api_gateway_integration.cors_integration,
  ]
  
  lifecycle {
    create_before_destroy = true
  }
}
