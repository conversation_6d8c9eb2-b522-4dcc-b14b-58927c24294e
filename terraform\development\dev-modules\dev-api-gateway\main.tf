data "aws_region" "current" {}

data "aws_caller_identity" "current" {}


resource "aws_api_gateway_rest_api" "rest_api" {
  name = var.rest_api_name
  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

//path part sets the endpoint path for the resource name





resource "aws_api_gateway_authorizer" "api_authorizer" {
  name            = "CognitoUserPoolAuthorizerapi"
  type            = "COGNITO_USER_POOLS"
  rest_api_id     = aws_api_gateway_rest_api.rest_api.id
  identity_source = "method.request.header.Authorization"
  provider_arns = [
    var.cognito_user_pool_arn
  ]
}

resource "aws_api_gateway_stage" "dev_stage" {
  deployment_id = aws_api_gateway_deployment.rest_api_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  stage_name    = "test"
  variables = {
    deployed_at = timestamp()
  }
  depends_on = [aws_api_gateway_account.rest_api_account]
}

resource "aws_api_gateway_deployment" "rest_api_deployment" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.rest_api_resource.id,
      aws_api_gateway_method.checkout_products_post_method.id,
      aws_api_gateway_method.contact_form_post_method.id,
      aws_api_gateway_method.rest_api_post_method.id,
      aws_api_gateway_method.rest_api_get_method.id,
      aws_api_gateway_method.webhooks_products_post_method.id,
      aws_api_gateway_method.cors_method.id,
      # New dev-products/{id} resources
      aws_api_gateway_resource.dev_products_resource.id,
      aws_api_gateway_resource.dev_products_id_resource.id,
      aws_api_gateway_method.dev_products_get_method.id,
      aws_api_gateway_method.dev_products_options_method.id,

      aws_api_gateway_integration.checkout_products_post_integration.id,
      aws_api_gateway_integration.contact_form_post_integration.id,
      aws_api_gateway_integration.rest_api_post_method_integration.id,
      aws_api_gateway_integration.rest_api_get_method_integration.id,
      aws_api_gateway_integration.webhooks_products_post_integration.id,
      aws_api_gateway_integration.cors_integration.id,
      # New dev-products/{id} integrations
      aws_api_gateway_integration.dev_products_get_integration.id,
      aws_api_gateway_integration.dev_products_options_integration.id,

      # Include a timestamp to force redeployment
      timestamp(),
    ]))
  }
  lifecycle {
    create_before_destroy = true
  }
}








#Cloudwatch for API
#---------------------------------------------------------
resource "aws_iam_role" "api_gateway_cloudwatch_logs" {
  name = "api_gateway_cloudwatch_logs-dev"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "api_gateway_cloudwatch_logs" {
  name = "api_gateway_cloudwatch_logs"
  role = aws_iam_role.api_gateway_cloudwatch_logs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:PutLogEvents",
          "logs:GetLogEvents",
          "logs:FilterLogEvents"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_api_gateway_account" "rest_api_account" {
  cloudwatch_role_arn = aws_iam_role.api_gateway_cloudwatch_logs.arn
}

resource "aws_cloudwatch_log_group" "cloudwatch_api" {
  name = "dev-cloudwatch_api"


  #   access_log_settings {
  #     destination_arn = aws_cloudwatch_log_group.cloudwatch_api.arn

  #     format = jsonencode({
  #       requestId               = "$context.requestId"
  #       sourceIp                = "$context.identity.sourceIp"
  #       requestTime             = "$context.requestTime"
  #       protocol                = "$context.protocol"
  #       httpMethod              = "$context.httpMethod"
  #       resourcePAth            = "$context.resourcePath"
  #       routeKey                = "$context.routeKey"
  #       status                  = "$context.status"
  #       responseLength          = "$context.responseLength"
  #       integrationErrorMessage = "context.$integrationErrorMessage"
  #       }
  #     )
  #   }
}


//API GATEWAY DOMAIN
//---------------------------------------------------------------------------------
resource "aws_api_gateway_domain_name" "api_domain" {
  domain_name              = var.sub_domain
  regional_certificate_arn = aws_acm_certificate_validation.certificate_validation.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  depends_on = [aws_acm_certificate_validation.certificate_validation]
}

# Example DNS record using Route53.
# Route53 is not specifically required; any DNS host can be used.
resource "aws_route53_record" "api_record" {
  name    = aws_api_gateway_domain_name.api_domain.domain_name
  type    = "A"
  zone_id = data.aws_route53_zone.zone.id

  alias {
    evaluate_target_health = true
    name                   = aws_api_gateway_domain_name.api_domain.regional_domain_name
    zone_id                = aws_api_gateway_domain_name.api_domain.regional_zone_id
  }
}

//HANDLING ERRORS
//---------------------------------------------------------------------------------
resource "aws_api_gateway_gateway_response" "response_4xx" {
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  response_type = "DEFAULT_4XX"
  response_templates = {
    "application/json" = "{'message':$context.error.messageString}"
  }
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'https://test.thealpinestudio.com'" // Using test domain for development
  }
}
resource "aws_api_gateway_gateway_response" "response_5xx" {
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  response_type = "DEFAULT_5XX"
  response_templates = {
    "application/json" = "{'message':$context.error.messageString}"
  }
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'https://test.thealpinestudio.com'" // Using test domain for development
  }
}
