#!/bin/bash

# Quick deployment script to fix the Lambda function for product ID fetch
# This deploys the updated Lambda function that handles ID-only queries

set -e

echo "🚀 Deploying Lambda function fix for product ID fetch..."

# Change to Lambda directory
cd lambda/dev/ListProducts

echo "📦 Building Lambda function..."
GOOS=linux GOARCH=amd64 go build -o bootstrap listProducts.go

echo "📦 Creating deployment package..."
zip dev-list-products.zip bootstrap

echo "☁️  Uploading to AWS Lambda..."
aws lambda update-function-code \
    --function-name dev-list-products \
    --zip-file fileb://dev-list-products.zip \
    --profile thealpinestudio \
    --region us-west-2

echo "🧹 Cleaning up..."
rm bootstrap dev-list-products.zip

echo "✅ Lambda function updated successfully!"
echo ""
echo "🔍 Testing the fix:"
echo "The Lambda function now supports fetching products by ID only using query parameters:"
echo "Example: https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test/dev-list-products?id=1743209557"
echo ""
echo "The frontend should now work correctly when fetching individual products!"
