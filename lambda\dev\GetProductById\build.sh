#!/bin/bash

# Variables
BUCKET_NAME="dev-thealpinestudio-lambda-functions-v1"
FILE_NAME="dev-get-product-by-id.zip"
LAMBDA_FUNCTION_NAME="dev-get-product-by-id"
REGION="us-west-2"

# Build the Lambda Function
GOOS=linux GOARCH=amd64 go build -o bootstrap getProductById.go || { echo 'Build failed'; exit 1; }

# Zip the bootstrap executable
zip $FILE_NAME bootstrap || { echo 'Zip failed'; exit 1; }

# Upload the zipped file to S3
aws s3 cp $FILE_NAME s3://$BUCKET_NAME/$FILE_NAME --region $REGION --profile thealpinestudio || { echo 'S3 upload failed'; exit 1; }

# Update the Lambda function code
aws lambda update-function-code --function-name $LAMBDA_FUNCTION_NAME --s3-bucket $BUCKET_NAME --s3-key $FILE_NAME --region $REGION --profile thealpinestudio || { echo 'Lambda update failed'; exit 1; }

# Clean up
rm bootstrap $FILE_NAME

echo "Lambda function $LAMBDA_FUNCTION_NAME updated successfully!"
