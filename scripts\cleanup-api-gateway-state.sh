#!/bin/bash

# <PERSON>ript to clean up orphaned API Gateway resources from Terraform state
# This addresses the "No integration defined for method" error

set -e

echo "🧹 Cleaning up orphaned API Gateway resources from Terraform state..."

# Change to the terraform directory
cd terraform/development/dev-env

# Initialize terraform if needed
terraform init

# List of potentially orphaned resources to remove from state
ORPHANED_RESOURCES=(
    "module.api_gateway.aws_api_gateway_resource.dev_products_resource"
    "module.api_gateway.aws_api_gateway_resource.dev_products_id_resource"
    "module.api_gateway.aws_api_gateway_method.dev_products_get_method"
    "module.api_gateway.aws_api_gateway_method.dev_products_options_method"
    "module.api_gateway.aws_api_gateway_integration.dev_products_get_integration"
    "module.api_gateway.aws_api_gateway_integration.dev_products_options_integration"
    "module.api_gateway.aws_api_gateway_method_response.dev_products_get_response"
    "module.api_gateway.aws_api_gateway_method_response.dev_products_options_response"
    "module.api_gateway.aws_api_gateway_integration_response.dev_products_get_integration_response"
    "module.api_gateway.aws_api_gateway_integration_response.dev_products_options_integration_response"
    "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_get_product_by_id"
)

# Remove orphaned resources from state
for resource in "${ORPHANED_RESOURCES[@]}"; do
    echo "🗑️  Attempting to remove $resource from state..."
    terraform state rm "$resource" 2>/dev/null || echo "   ℹ️  Resource $resource not found in state (this is OK)"
done

echo "✅ State cleanup completed!"

# Now run a plan to see what needs to be done
echo "📋 Running terraform plan to verify state..."
terraform plan -detailed-exitcode || {
    exit_code=$?
    if [ $exit_code -eq 2 ]; then
        echo "✅ Terraform plan shows changes are needed - this is expected"
        exit 0
    else
        echo "❌ Terraform plan failed with exit code $exit_code"
        exit $exit_code
    fi
}

echo "✅ Terraform state is clean and ready for deployment!"
