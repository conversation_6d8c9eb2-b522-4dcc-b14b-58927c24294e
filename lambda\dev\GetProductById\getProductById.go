package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"

	// AWS SDK v1
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

// Define structures for Product, Size, and Price
type Product struct {
	ID            string   `json:"id" dynamodbav:"ID"`
	Title         string   `json:"title" dynamodbav:"Title"`
	Description   string   `json:"description" dynamodbav:"Description"`
	Category      string   `json:"category" dynamodbav:"category"`
	Subcategories []string `json:"subcategories" dynamodbav:"subcategories"`
	Sizes         []Size   `json:"sizes" dynamodbav:"sizes"`
	ImageUrl      string   `json:"imageUrl" dynamodbav:"ImageUrl"`
	Sold          *bool    `json:"sold" dynamodbav:"sold"`
}

type Size struct {
	Size  string `json:"size" dynamodbav:"size"`
	Price Price  `json:"price" dynamodbav:"price"`
}

type Price struct {
	Amount   float64 `json:"amount" dynamodbav:"amount"`
	Currency string  `json:"currency" dynamodbav:"currency"`
}

var (
	db        *dynamodb.DynamoDB
	tableName string
)

func init() {
	// Initialize AWS session
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String("us-west-2"),
	})
	if err != nil {
		fmt.Printf("Failed to create AWS session: %v\n", err)
		panic(err)
	}

	db = dynamodb.New(sess)
	tableName = os.Getenv("TABLE_NAME")
	if tableName == "" {
		fmt.Println("TABLE_NAME environment variable is not set")
		panic("TABLE_NAME environment variable is not set")
	}
}

func GetProductByIdHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	fmt.Println("Received request:", request)
	productID := request.PathParameters["id"]
	if productID == "" {
		fmt.Println("Product ID is missing in the request.")
		return generateErrorResponse(400, "Product ID is missing")
	}

	// Use Query operation to find the product by ID
	input := &dynamodb.QueryInput{
		TableName:              aws.String(tableName),
		KeyConditionExpression: aws.String("#id = :id"),
		ExpressionAttributeNames: map[string]*string{
			"#id": aws.String("ID"),
		},
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":id": {
				S: aws.String(productID),
			},
		},
	}

	fmt.Printf("Querying items with ID: %s from table: %s\n", productID, tableName)
	result, err := db.Query(input)
	if err != nil {
		fmt.Println("Error calling Query:", err.Error())
		return generateErrorResponse(500, "Internal Server Error")
	}

	if len(result.Items) == 0 {
		fmt.Printf("Product with ID %s not found.\n", productID)
		return generateErrorResponse(404, "Product not found")
	}

	// Get the first item returned
	product := Product{}
	err = dynamodbattribute.UnmarshalMap(result.Items[0], &product)
	if err != nil {
		fmt.Println("Failed to unmarshal DynamoDB item:", err)
		return generateErrorResponse(500, "Internal Server Error")
	}

	// Update image URL if necessary
	imageBucketName := os.Getenv("IMAGE_BUCKET_NAME")
	if imageBucketName != "" && strings.Contains(product.ImageUrl, "admin-thealpinestudio-lambda-functions") {
		// Update image URL to use the new bucket
		imageKey := strings.Split(product.ImageUrl, "/")
		if len(imageKey) > 0 {
			product.ImageUrl = "https://" + imageBucketName + ".s3.amazonaws.com/" + imageKey[len(imageKey)-1]
		}
	}

	fmt.Printf("Unmarshalled product: %+v\n", product)
	body, err := json.Marshal(product)
	if err != nil {
		fmt.Println("Failed to marshal product to JSON:", err)
		return generateErrorResponse(500, "Internal Server Error")
	}

	fmt.Println("Successfully fetched and marshalled product.")
	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(body),
		Headers: map[string]string{
			"Content-Type":                     "application/json",
			"Access-Control-Allow-Origin":      "*",
			"Access-Control-Allow-Methods":     "GET, OPTIONS",
			"Access-Control-Allow-Headers":     "Content-Type,Authorization",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func generateErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]string{"message": message}
	body, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Body:       string(body),
		Headers: map[string]string{
			"Content-Type":                     "application/json",
			"Access-Control-Allow-Origin":      "*",
			"Access-Control-Allow-Methods":     "GET, OPTIONS",
			"Access-Control-Allow-Headers":     "Content-Type,Authorization",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	lambda.Start(GetProductByIdHandler)
}
