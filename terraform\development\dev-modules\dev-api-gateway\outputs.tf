output "api_gateway_arn" {
  description = "ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.rest_api.arn
}
# Temporarily comment out to allow state cleanup
# output "api_gateway_url" {
#   description = "URL of the API Gateway"
#   value       = aws_api_gateway_stage.dev_stage.invoke_url
# }
output "certificate_arn" {
  value = aws_acm_certificate.certificate.arn
}
output "api_gateway_id" {
  value = aws_api_gateway_rest_api.rest_api.id
}
